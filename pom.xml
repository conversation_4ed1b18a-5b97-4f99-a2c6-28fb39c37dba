<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>fm.lizhi.ocean.wavecenter</groupId>
    <artifactId>web-ocean-wavecenter</artifactId>
    <version>1.1.5</version>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <lz-common-dependencies-bom.version>2.0.53</lz-common-dependencies-bom.version>
        <wavecenter.version>2.0.3-SNAPSHOT</wavecenter.version>

        <lz-commons-queue.version>3.0.34</lz-commons-queue.version>
        <lz-common-kafka.version>1.0.34</lz-common-kafka.version>

        <!-- ====================        基础架构服务依赖        ==================== -->

        <lz-commons-rome-push-cm-api.version>1.2.0</lz-commons-rome-push-cm-api.version>
        <sonar-maven-plugin.version>3.11.0.3922</sonar-maven-plugin.version>

        <!-- ====================        第二方框架属性        ==================== -->

        <lamp-common.version>1.1.6</lamp-common.version>

        <!-- ====================        第三方框架属性        ==================== -->
        <lombok.verison>1.18.30</lombok.verison>
        <commons-collections4.version>4.4</commons-collections4.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <hutool.version>5.8.27</hutool.version>
        <romefs.version>2.1</romefs.version>
        <kotlin-stdlib.version>1.3.50</kotlin-stdlib.version>
        <easyexcel.version>3.3.2</easyexcel.version>


        <!-- ====================        第二方服务依赖        ==================== -->

        <lz-ocean-wave-api.version>1.0.19</lz-ocean-wave-api.version>
    </properties>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib</artifactId>
                <version>${kotlin-stdlib.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.common</groupId>
                <artifactId>lz-common-dependencies-bom</artifactId>
                <version>${lz-common-dependencies-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <dependencies>


        <!--
        ========    消息中间件     ==========
-->
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-queue</artifactId>
            <version>${lz-commons-queue.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>kafka-client-spring-config</artifactId>
            <version>${lz-common-kafka.version}</version>
        </dependency>

        <!--        审核接口 -->
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-content-review-api</artifactId>
            <version>4.2.8</version>
        </dependency>

        <!--罗马上传 start -->

        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>lz-common-romefs-javasdk</artifactId>
            <version>${romefs.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>kotlin-stdlib</artifactId>
                    <groupId>org.jetbrains.kotlin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>kotlin-stdlib-common</artifactId>
                    <groupId>org.jetbrains.kotlin</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>${kotlin-stdlib.version}</version>
        </dependency>
        <!--        罗马上传end-->

        <!-- ====================        wavecenterDC依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-api</artifactId>
            <version>${wavecenter.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-base</artifactId>
            <version>${wavecenter.version}</version>
        </dependency>

        <!-- ====================        基础架构的依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-serviceclient</artifactId>
        </dependency>
        <!-- 日记打印，解决logback配置文件不生效问题 -->
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>lz-common-logging-starter</artifactId>
        </dependency>
        <!-- apollo配置中心客户端 -->
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-config</artifactId>
        </dependency>
        <!-- 单元测试 -->
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-unit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-rome-push-cm-api</artifactId>
            <version>${lz-commons-rome-push-cm-api.version}</version>
        </dependency>
        <!-- 熔断限流 -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-all</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>datastore-redis-spring-boot-starter</artifactId>
        </dependency>

        <!-- ====================        第二方框架属性        ==================== -->

        <dependency>
            <groupId>fm.lizhi.ocean.lamp</groupId>
            <artifactId>lamp-common-logging</artifactId>
            <version>${lamp-common.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.lamp</groupId>
            <artifactId>lamp-common-config</artifactId>
            <version>${lamp-common.version}</version>
        </dependency>

        <!-- ====================        第三方依赖        ==================== -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <!-- 排除日记依赖 -->
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- cat -->
        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.verison}</version>
        </dependency>
        <!-- 运行时类型映射 -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <!-- 支持ThreadLocal的线程池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>${transmittable-thread-local.version}</version>
        </dependency>
        <!-- apache集合类新版, 推荐 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>${commons-collections4.version}</version>
        </dependency>
        <!-- apache集合类, 不推荐 -->
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>${commons-collections.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <!-- easyexcel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
            <version>5.0.0-RC2</version>
        </dependency>
        <!--  飞书sdk          -->
        <dependency>
            <groupId>com.larksuite.oapi</groupId>
            <artifactId>oapi-sdk</artifactId>
            <version>2.1.1</version>
        </dependency>


        <!-- ====================        第二方服务依赖        ==================== -->

        <!-- 创作者基础DC -->
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>lz-ocean-wave-api</artifactId>
            <version>${lz-ocean-wave-api.version}</version>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>/src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <descriptors>
                        <descriptor>src/main/assembly/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.verison}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.4</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.0.0-M1</version>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>${sonar-maven-plugin.version}</version>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>central</id>
            <name>Central</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
        </repository>
        <repository>
            <id>codehaus-snapshots</id>
            <name>Codehaus Snapshots</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>central</id>
            <name>Maven Plugin Repository</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
            <layout>default</layout>
            <snapshots/>
            <releases>
                <updatePolicy>never</updatePolicy>
            </releases>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>release</id>
            <url>http://maven.lizhi.fm:8081/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://maven.lizhi.fm:8081/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>
