package fm.lizhi.ocean.wavecenter.web.module.sign.controller;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.*;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestPlayerHallInfo;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponsePlayerHallInfo;
import fm.lizhi.ocean.wavecenter.api.sign.service.GuildManageService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.FileExportHandler;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import fm.lizhi.ocean.wavecenter.web.module.sign.convert.GuildManageConvert;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.*;
import fm.lizhi.ocean.wavecenter.web.util.MyDateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/21 16:54
 */
@RestController
@RequestMapping("/guild/manage")
public class GuildManageController {

    private static final Logger log = LoggerFactory.getLogger(GuildManageController.class);
    @Autowired
    private GuildManageService guildManageService;
    @Autowired
    private DataScopeHandler dataScopeHandler;

    @Autowired
    private FileExportHandler fileExportHandler;


    /**
     * 查询家族长信息
     *
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = RoleEnum.FAMILY)
    @GetMapping("fullInfo")
    public ResultVO<GuildFullInfoVo> fullInfo() {
        Long familyId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        Result<GuildFullInfoBean> result = guildManageService.getFullInfo(GetFullInfoReq.builder()
                .appId(appId)
                .familyId(familyId)
                .build());
        if (RpcResult.isFail(result)) {
            log.error("getFullInfo fail. rCode={},familyId={},appId={}", result.rCode(), familyId, appId);
            return ResultVO.failure();
        }

        return ResultVO.success(GuildManageConvert.I.fullInfoBean2Vo(result.target()));
    }

    /**
     * 查询签约厅列表
     *
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
    @GetMapping("signRoomPageList")
    public ResultVO<PageVO<GuildSignRoomVo>> signRoomPageList(@Validated GMCSignRoomPageListReqVo req) {
        if (!dataScopeHandler.checkParamRoom(req.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Date signStartDate = MyDateUtil.getDayStart(req.getSignStartDate());
        Date signEndDate = MyDateUtil.getDayEnd(req.getSignEndDate());
        Date expireStartDate = MyDateUtil.getDayStart(req.getExpireStartDate());
        Date expireEndDate = MyDateUtil.getDayEnd(req.getExpireEndDate());
        Date stopStartDate = MyDateUtil.getDayStart(req.getStopStartDate());
        Date stopEndDate = MyDateUtil.getDayEnd(req.getStopEndDate());

        GMSSignRoomPageListReq reqBean = GMSSignRoomPageListReq.builder()
                .roomId(req.getRoomId())
                .signStartDate(signStartDate)
                .signEndDate(signEndDate)
                .expireStartDate(expireStartDate)
                .expireEndDate(expireEndDate)
                .stopStartDate(stopStartDate)
                .stopEndDate(stopEndDate)
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .familyId(ContextUtils.getContext().getSubjectId())
                .pageNo(req.getPageNo())
                .pageSize(req.getPageSize())
                .signStatus(req.getSignStatus())
                .roomIds(ContextUtils.getContext().getRoomResource())
                .build();
        Result<PageBean<RoomSignInfoBean>> result = guildManageService.signRoomPageList(reqBean);
        if (RpcResult.isFail(result)) {
            log.error("signRoomPageList fail, rCode={}, reqBean={}", result.rCode(), JsonUtil.dumps(reqBean));
            return ResultVO.failure();
        }

        PageBean<RoomSignInfoBean> pageBean = result.target();
        List<GuildSignRoomVo> voList = GuildManageConvert.I.roomSignInfoBeans2Vos(pageBean.getList());
        return ResultVO.success(PageVO.of(pageBean.getTotal(), voList));
    }

    /**
     * 导出签约主播列表
     *
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @GetMapping("signPlayerPageList/export")
    public ResultVO<Void> signPlayerPageListExport(@Validated GMCSignPlayerPageListReqVo req) {
        if (!dataScopeHandler.checkParamRoom(req.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }
        if (!dataScopeHandler.checkParamPlayerId(req.getPlayerId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Date signStartDate = MyDateUtil.getDayStart(req.getSignStartDate());
        Date signEndDate = MyDateUtil.getDayEnd(req.getSignEndDate());
        Date expireStartDate = MyDateUtil.getDayStart(req.getExpireStartDate());
        Date expireEndDate = MyDateUtil.getDayEnd(req.getExpireEndDate());
        Date stopStartDate = MyDateUtil.getDayStart(req.getStopStartDate());
        Date stopEndDate = MyDateUtil.getDayEnd(req.getStopEndDate());

        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        Long queryRoomId = dataScopeHandler.getRoomForRoomOrDefault(req.getRoomId());

        String fileName = fileExportHandler.genFileName("签约主播列表");
        return fileExportHandler.exportFile(fileName, SignPlayerExcelVo.class, (pageNo, pageSize)->{
            GMSSignPlayerPageListReq reqBean = GMSSignPlayerPageListReq.builder()
                .roomId(queryRoomId)
                .familyId(familyId)
                .playerId(req.getPlayerId())
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .signStartDate(signStartDate)
                .signEndDate(signEndDate)
                .expireStartDate(expireStartDate)
                .expireEndDate(expireEndDate)
                .stopStartDate(stopStartDate)
                .stopEndDate(stopEndDate)
                .settleMax(req.getSettleMax())
                .settleMin(req.getSettleMin())
                .pageNo(pageNo)
                .pageSize(pageSize)
                .signStatus(req.getSignStatus())
                .roomIds(ContextUtils.getContext().getRoomResource())
                .build();

            Result<PageBean<SignPlayerInfoBean>> result = guildManageService.signPlayerPageList(reqBean);
            if (RpcResult.isFail(result)) {
                log.error("signPlayerPageList export fail, rCode={}, reqBean={}", result.rCode(), JsonUtil.dumps(reqBean));
                return PageVO.empty();
            }

            PageBean<SignPlayerInfoBean> pageBean = result.target();
            List<SignPlayerExcelVo> voList = GuildManageConvert.I.signPlayerInfoBeans2ExcelVos(pageBean.getList());
            return PageVO.of(pageBean.getTotal(), voList);
        });
    }

    /**
     * 查询签约主播列表
     *
     * @return
     */
    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.ROOM, RoleEnum.FAMILY_ADMIN})
    @GetMapping("signPlayerPageList")
    public ResultVO<PageVO<SignPlayerVo>> signPlayerPageList(@Validated GMCSignPlayerPageListReqVo req) {
        if (!dataScopeHandler.checkParamRoom(req.getRoomId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }
        if (!dataScopeHandler.checkParamPlayerId(req.getPlayerId())) {
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }

        Date signStartDate = MyDateUtil.getDayStart(req.getSignStartDate());
        Date signEndDate = MyDateUtil.getDayEnd(req.getSignEndDate());
        Date expireStartDate = MyDateUtil.getDayStart(req.getExpireStartDate());
        Date expireEndDate = MyDateUtil.getDayEnd(req.getExpireEndDate());
        Date stopStartDate = MyDateUtil.getDayStart(req.getStopStartDate());
        Date stopEndDate = MyDateUtil.getDayEnd(req.getStopEndDate());

        Long familyId = dataScopeHandler.getFamilyForFamilyOrRoom();
        Long queryRoomId = dataScopeHandler.getRoomForRoomOrDefault(req.getRoomId());

        GMSSignPlayerPageListReq reqBean = GMSSignPlayerPageListReq.builder()
                .roomId(queryRoomId)
                .familyId(familyId)
                .playerId(req.getPlayerId())
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .signStartDate(signStartDate)
                .signEndDate(signEndDate)
                .expireStartDate(expireStartDate)
                .expireEndDate(expireEndDate)
                .stopStartDate(stopStartDate)
                .stopEndDate(stopEndDate)
                .settleMax(req.getSettleMax())
                .settleMin(req.getSettleMin())
                .pageNo(req.getPageNo())
                .pageSize(req.getPageSize())
                .signStatus(req.getSignStatus())
                .roomIds(ContextUtils.getContext().getRoomResource())
                .build();

        Result<PageBean<SignPlayerInfoBean>> result = guildManageService.signPlayerPageList(reqBean);
        if (RpcResult.isFail(result)) {
            log.error("signPlayerPageList fail, rCode={}, reqBean={}", result.rCode(), JsonUtil.dumps(reqBean));
            return ResultVO.failure();
        }
        PageBean<SignPlayerInfoBean> pageBean = result.target();
        List<SignPlayerVo> voList = GuildManageConvert.I.signPlayerInfoBeans2Vos(pageBean.getList());
        return ResultVO.success(PageVO.of(pageBean.getTotal(), voList));
    }

    @VerifyUserToken
    @PermissionCheck(passRole = {RoleEnum.ROOM, RoleEnum.PLAYER})
    @GetMapping("playerHallInfo")
    public ResultVO<PlayerHallInfoVO> playerHallInfo(PlayerHallInfoReqVO req) {
        Long playerId = req.getPlayerId();
        if (ContextUtils.getContext().isPlayer()) {
            playerId = ContextUtils.getContext().getUserId();
        }

        RequestPlayerHallInfo reqPlayerHallInfo = new RequestPlayerHallInfo();
        reqPlayerHallInfo.setPlayerId(playerId);
        reqPlayerHallInfo.setAppId(ContextUtils.getBusinessEvnEnum().getAppId());

        Result<ResponsePlayerHallInfo> result = guildManageService.playerHallInfo(reqPlayerHallInfo);
        if (RpcResult.isFail(result)) {
            log.error("playerHallInfo fail, rCode={}", result.rCode());
            return ResultVO.failure("当前用户不存在签约厅主，请确认后再尝试");
        }

        return ResultVO.success(GuildManageConvert.I.playerHallInfoResp2Vo(result.target()));
    }

}
