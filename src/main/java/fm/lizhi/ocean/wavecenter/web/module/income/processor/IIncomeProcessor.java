package fm.lizhi.ocean.wavecenter.web.module.income.processor;

import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeDetailBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.RoomSignPlayerIncomeBean;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.functional.ExportDataQuery;
import fm.lizhi.ocean.wavecenter.web.common.processor.BusinessEnvAwareProcessor;

/**
 * <AUTHOR>
 * @date 2024/5/20 19:31
 */
public interface IIncomeProcessor extends BusinessEnvAwareProcessor {

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return IIncomeProcessor.class;
    }

    ResultVO<Void> roomIncomeDetailExport(ExportDataQuery<RoomIncomeDetailBean> dataQuery);

    ResultVO<Void> roomSignPlayerExport(ExportDataQuery<RoomSignPlayerIncomeBean> dataQuery);
}
