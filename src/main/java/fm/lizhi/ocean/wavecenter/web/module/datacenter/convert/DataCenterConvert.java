package fm.lizhi.ocean.wavecenter.web.module.datacenter.convert;

import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyAuthBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyLevelInfoBean;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.web.module.datacenter.vo.*;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/17 17:21
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface DataCenterConvert {

    DataCenterConvert I = Mappers.getMapper(DataCenterConvert.class);

    @Named("digitToChinese")
    default String digitToChinese(String digit){
        if (StrUtil.isBlank(digit) || !NumberUtil.isNumber(digit)) {
            return "";
        }

        return "第" + NumberChineseFormatter.format(Long.parseLong(digit), false, false) + "梯队";
    }

    @Named("convertRate")
    default String convertRate(String bigDecimalStr){
        if (bigDecimalStr == null) {
            return CalculateUtil.formatPercent("0");
        }
        BigDecimal bigDecimal = new BigDecimal(bigDecimalStr);
        return CalculateUtil.formatPercent(bigDecimal);
    }


    IncomeVo incomeBean2Vo(IncomeBean bean);

    @Mappings({
            @Mapping(target = "incomePlayerCnt", source = "playerPayCount")
    })
    GuildAssessmentInfoVo guildAssessmentInfoBean2Vo(GuildAssessmentInfoBean bean);

    @Mapping(target = "playerInfo", ignore = true)
    PlayerAssessmentInfoVo playerAssessmentInfoBean2Vo(PlayerAssessmentInfoBean bean);

    @Mappings({
            @Mapping(target = "incomePlayerCnt", source = "playerPayCount"),
            @Mapping(target = "examinationFlow.currentEchelonName", source = "examinationFlow.currentEchelonName", qualifiedByName = "digitToChinese"),
            @Mapping(target = "nextEchelon.currentEchelonName", source = "nextEchelon.currentEchelonName", qualifiedByName = "digitToChinese"),
            @Mapping(target = "examinationFlow.ratio", source = "examinationFlow.ratio"),
            @Mapping(target = "nextEchelon.ratio", source = "nextEchelon.ratio"),
            @Mapping(target = "roomInfo", ignore = true)
    })
    RoomAssessmentInfoVo roomAssessmentInfoBean2Vo(RoomAssessmentInfoBean bean);

    @Mapping(target = "flushTime", ignore = true)
    RoomPlayerPerformanceResVo roomPlayerPerformanceResBean2Vo(RoomPlayerPerformanceResBean bean);

    IndicatorExtensionVo extensionBean2Vo(IndicatorExtensionBean bean);

    IndicatorVo indicatorBean2Vo(IndicatorBean bean);

    List<IndicatorVo> indicatorBeans2Vos(List<IndicatorBean> beans);

    GuildRoomPerformanceResVo guildRoomPerformanceResBean2Vo(GuildRoomPerformanceResBean bean);

    @Mappings({
            @Mapping(target = "familyId", source = "familyUserBand")
    })
    FamilyAuthVo familyAuthBean2Vo(FamilyAuthBean bean);

    FamilyLevelInfoVO familyLevelInfoBean2VO(FamilyLevelInfoBean bean);

    CountDataVo countDataBean2Vo(CountDataBean bean);

    List<CountDataVo> countDataBeans2Vos(List<CountDataBean> beans);

    IndicatorTrendResVo indicatorTrendResBean2Vo(IndicatorTrendResBean bean);

    RankVo rankBean2Vo(RankBean bean);

    List<RankVo> rankBeans2Vos(List<RankBean> beans);

    PlayerRankVo playerRankBean2Vo(PlayerRankBean bean);

    List<PlayerRankVo> playerRankBeans2Vos(List<PlayerRankBean> beans);

    List<RoomRankVo> roomRankBeans2Vos(List<RoomRankBean> beans);

    GuildIncomeSummaryVo guildIncomeSummaryBean2Vo(GuildIncomeSummaryBean bean);

    RoomIncomeSummaryVo roomIncomeSummaryBean2Vo(RoomIncomeSummaryBean bean);

    @Mapping(target = "roomName", source = "bean.roomInfo.name")
    @Mapping(target = "njId", source = "bean.roomInfo.id")
    RoomRankExportVo convertRoomRankExportVo(RoomRankBean bean);
    List<RoomRankExportVo> convertRoomRankExportVos(List<RoomRankBean> list);

    @Mapping(target = "roomName", source = "bean.roomInfo.name")
    @Mapping(target = "playerName", source = "bean.playerInfo.name")
    @Mapping(target = "playerBand", source = "bean.playerInfo.band")
    @Mapping(target = "njId", source = "bean.roomInfo.id")
    PlayerRankExportVo convertPlayerRankExportVo(PlayerRankBean bean);
    List<PlayerRankExportVo> convertPlayerRankExportVos(List<PlayerRankBean> list);
}
