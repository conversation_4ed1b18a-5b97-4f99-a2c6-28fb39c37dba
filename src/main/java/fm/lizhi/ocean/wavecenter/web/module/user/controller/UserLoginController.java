package fm.lizhi.ocean.wavecenter.web.module.user.controller;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.RoleInfoAuthRefBean;
import fm.lizhi.ocean.wavecenter.api.permissions.service.RoleService;
import fm.lizhi.ocean.wavecenter.api.user.bean.*;
import fm.lizhi.ocean.wavecenter.api.user.constants.QrCodeStatus;
import fm.lizhi.ocean.wavecenter.api.user.service.UserLoginService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.context.Header;
import fm.lizhi.ocean.wavecenter.web.module.permission.convert.RoleConvert;
import fm.lizhi.ocean.wavecenter.web.module.user.handler.UserLoginHandler;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/12 14:09
 */
@Slf4j
@RestController
@RequestMapping("/user/login")
public class UserLoginController {

    @Autowired
    private UserLoginService userLoginService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private UserLoginHandler userLoginHandler;

    /**
     * 获取当前登录用户信息
     * @return
     */
    @VerifyUserToken
    @GetMapping("loginUserInfo")
    public ResultVO<LoginUserInfoVo> getLoginUserInfo(){
        long userId = ContextUtils.getContext().getUserId();
        log.info("getLoginUserInfo, userId={},token={}", userId, ContextUtils.getContext().getToken());
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        return ResultVO.success(userLoginHandler.getLoginUserInfo(appId, userId, ContextUtils.getContext().getToken()));
    }

    /**
     * 退出登录
     * @return
     */
    @VerifyUserToken
    @PostMapping("out")
    public ResultVO<Void> out(){
        //需要用户ID和设备ID
        long userId = ContextUtils.getContext().getUserId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String deviceId = ContextUtils.getContext().getHeader().getDeviceId();
        //调用DC退出
        Result<Void> result = userLoginService.logout(appId, userId, deviceId);
        int rCode = result.rCode();
        if (RpcResult.isFail(result)) {
            log.error("getLoginUserInfo,error,userId={},rCode={}", userId, rCode);
            return ResultVO.failure();
        }
        return ResultVO.success();
    }

    /**
     * 获取二维码登录结果
     * @param qrCodeKey
     * @return
     */
    @VerifyUserToken(required = false)
    @GetMapping("/qrCode/result")
    public ResultVO<QrCodeResultVo> qrCodeResult(@RequestParam("qrCodeKey") String qrCodeKey){
        if (StringUtils.isEmpty(qrCodeKey)) {
            return ResultVO.failure(MsgCodes.PARAM_ERROR);
        }
        //调用dc获取token
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<QrCodeResultBean> tokenResult = userLoginService.getQrCodeToken(appId, qrCodeKey);
        if (RpcResult.isFail(tokenResult)) {
            int rCode = tokenResult.rCode();
            if (rCode == UserLoginService.QR_CODE_NOT_EXIST) {
                return ResultVO.failure(MsgCodes.QR_CODE_NOT_EXIST);
            }
            return ResultVO.failure();
        }
        QrCodeResultBean qrCodeResultBean = tokenResult.target();

        QrCodeResultVo qrCodeResultVo = new QrCodeResultVo();
        qrCodeResultVo.setLoginStatus(qrCodeResultBean.getLoginStatus());
        if (qrCodeResultBean.getLoginStatus() != QrCodeStatus.SUCCESS) {
            log.info("qrCodeResult,qrCodeResultBean={}", JsonUtil.dumps(qrCodeResultBean));
            return ResultVO.success(qrCodeResultVo);
        }

        Long userId = qrCodeResultBean.getUserId();
        log.info("qrCodeResult,userId={}", userId);
        qrCodeResultVo.setAccessToken(qrCodeResultBean.getAccessToken());
        qrCodeResultVo.setRefreshToken(qrCodeResultBean.getRefreshToken());

        //查询角色
        //查询被授权角色列表
        Result<List<RoleInfoAuthRefBean>> roleResult = roleService.getUserAuthRoles(appId, userId);
        if (RpcResult.isFail(roleResult)) {
            log.error("qrCodeResult,getUserAuthRoles,error,rCode={}", roleResult.rCode());
            return ResultVO.failure("获取角色信息失败");
        }

        List<RoleInfoAuthRefBean> roleList = roleResult.target();
        //没有授权角色：返回菜单, 设置登录角色 rCode=登录成功
        if (CollectionUtils.isEmpty(roleList)) {
            //设置角色为自己
            Result<Void> result = userLoginService.saveUserRole(SaveUserRoleReqBean.builder()
                    .userId(userId)
                    .appId(appId)
                    .deviceId(ContextUtils.getContext().getHeader().getDeviceId())
                    .build());
            if (RpcResult.isFail(result)) {
                return ResultVO.failure();
            }

            LoginUserInfoVo loginUserInfo = userLoginHandler.getLoginUserInfo(appId, userId, qrCodeResultBean.getAccessToken());
            qrCodeResultVo.setUserInfo(loginUserInfo.getUserInfo());
            qrCodeResultVo.setPermissionInfo(loginUserInfo.getPermissionInfo());
            qrCodeResultVo.setLoginType(loginUserInfo.getLoginType());
            return ResultVO.success(qrCodeResultVo);
        }

        //有授权角色：返回角色列表 rCode=请选择角色
        //设置用户信息
        LoginUserInfoVo loginUserInfo = userLoginHandler.getLoginUserInfo(appId, userId, "");
        qrCodeResultVo.setUserInfo(loginUserInfo.getUserInfo());
        qrCodeResultVo.setRoles(RoleConvert.I.authRefInfoBeans2InfoVos(roleList));
        log.info("qrCodeResult,qrCodeResultVo={}", JsonUtil.dumps(qrCodeResultVo));
        return new ResultVO<>(MsgCodes.CHOSE_DATA_SCOPE.getCode(), null, qrCodeResultVo);
    }

    /**
     * 二维码登录
     * @return
     */
    @VerifyUserToken(required = false)
    @PostMapping("/qrCode")
    public ResultVO<Void> qrCodeLogin(@Validated @RequestBody QrCodeLoginParamVo paramVo){
        //调用DC登录
        BusinessEvnEnum businessEvnEnum = ContextUtils.getBusinessEvnEnum();
        Header header = ContextUtils.getContext().getHeader();
        Result<Void> result = userLoginService.qrCodeLogin(QrCodeLoginParamBean.builder()
                .appId(businessEvnEnum.appId())
                .ip(header.getIp())
                .qrCodeKey(paramVo.getQrCodeKey())
                .businessToken(paramVo.getBusinessToken())
                .deviceType(header.getDeviceType())
                .clientVersion(header.getClientVersion())
                .build());
        if (RpcResult.isFail(result)) {
            int rCode = result.rCode();
            log.warn("qrCodeLogin,rCode={}", rCode);
            if (rCode == UserLoginService.NOT_IN_WHITELIST) {
                return ResultVO.failure("创作服务中心正在内测试用中，请耐心等待平台通知开放登陆");
            }
            if (rCode == UserLoginService.AUTH_FAIL) {
                return ResultVO.failure(MsgCodes.LOGIN_AUTH_FAIL);
            }
            if (rCode == UserLoginService.USER_NOT_EXIST) {
                return ResultVO.failure(MsgCodes.USER_NOT_FOUND);
            }
            if (rCode == UserLoginService.USER_NO_VERIFY) {
                return ResultVO.failure(MsgCodes.LOGIN_USER_NO_VERIFY);
            }
            if (rCode == UserLoginService.USER_INVALID) {
                return ResultVO.failure(MsgCodes.LOGIN_USER_INVALID.getCode(), result.getMessage());
            }
            if (rCode == UserLoginService.USER_BAN) {
                return ResultVO.failure(MsgCodes.LOGIN_USER_BAN);
            }
            if (rCode == UserLoginService.RISK_FAILED) {
                return ResultVO.failure(MsgCodes.LOGIN_RISK_FAILED);
            }
            if (rCode == UserLoginService.QR_CODE_NOT_EXIST) {
                return ResultVO.failure(MsgCodes.QR_CODE_NOT_EXIST);
            }
            return ResultVO.failure();
        }
        return ResultVO.success();
    }

    /**
     * 开始扫描二维码
     * @param qrCodeKey
     * @return
     */
    @VerifyUserToken(required = false)
    @PostMapping("qrCode/begin/scan")
    public ResultVO<Void> qrCodeBeginScan(@RequestParam String qrCodeKey){
        if (StringUtils.isBlank(qrCodeKey)) {
            return ResultVO.failure("二维码为空");
        }
        //调用dc 修改二维码状态
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<Void> result = userLoginService.beginScanQrCode(appId, qrCodeKey);
        if (RpcResult.isFail(result)) {
            log.error("qrCodeBeginScan,rCode={}", result.rCode());
            if (result.rCode() == UserLoginService.QR_CODE_NOT_EXIST) {
                return ResultVO.failure("二维码失效");
            }
            return ResultVO.failure();
        }
        return ResultVO.success();
    }

    /**
     * 创建二维码
     * @return
     */
    @VerifyUserToken(required = false)
    @PostMapping("/create/qrcode")
    public ResultVO<CreateQrCodeResVo> createQrCode(@RequestParam(required = false) String oldQrCodeKey){
        //调用DC，传appId 和 旧QRCode
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String deviceId = ContextUtils.getContext().getHeader().getDeviceId();
        if (StringUtils.isBlank(deviceId)) {
            return ResultVO.failure("登录设备异常");
        }

        Result<CreateQrCodeResBean> result = userLoginService.createQrCode(appId, oldQrCodeKey, deviceId);
        if (RpcResult.isFail(result)) {
            log.error("createQrCode,rCode={}", result.rCode());
            return ResultVO.failure();
        }
        CreateQrCodeResVo createQrCodeResVo = new CreateQrCodeResVo()
                .setLoginH5Url(result.target().getLoginH5Url())
                .setQrcodeKey(result.target().getQrcodeKey());
        log.info("createQrCode,createQrCodeResVo={}", JsonUtil.dumps(createQrCodeResVo));
        return ResultVO.success(createQrCodeResVo);
    }

    /**
     * 手机号码登录
     * @param paramVo
     * @return
     */
    @VerifyUserToken(required = false)
    @PostMapping("phone")
    public ResultVO<UserTokenVo> phone(@Validated @RequestBody PhoneLoginParamVo paramVo){
        //获取appId
        Header header = ContextUtils.getContext().getHeader();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        //获取IP
        String ip = header.getIp();
        //获取设备ID
        String deviceId = header.getDeviceId();
        if (StringUtils.isBlank(deviceId)) {
            return ResultVO.failure("登录设备异常");
        }

        //调用DC获取token
        Result<UserTokenBean> tokenResult = userLoginService.phoneLogin(PhoneLoginParamBean.builder()
                .appId(appId)
                .ip(ip)
                .phone(paramVo.getPhone())
                .authCode(paramVo.getAuthCode())
                .deviceId(deviceId)
                .deviceType(header.getDeviceType())
                .clientVersion(header.getClientVersion())
                .build());
        if (RpcResult.isFail(tokenResult)) {
            int tokenRCode = tokenResult.rCode();
            log.warn("phoneLogin,rCode={}", tokenRCode);
            if (tokenRCode == UserLoginService.NOT_IN_WHITELIST) {
                return ResultVO.failure("创作服务中心正在内测试用中，请耐心等待平台通知开放登陆");
            }
            if (tokenRCode == UserLoginService.AUTH_FAIL) {
                return ResultVO.failure(MsgCodes.LOGIN_AUTH_FAIL);
            }
            if (tokenRCode == UserLoginService.USER_NOT_EXIST) {
                return ResultVO.failure(MsgCodes.USER_NOT_FOUND);
            }
            if (tokenRCode == UserLoginService.USER_NO_VERIFY) {
                return ResultVO.failure(MsgCodes.LOGIN_USER_NO_VERIFY);
            }
            if (tokenRCode == UserLoginService.USER_INVALID) {
                return ResultVO.failure(MsgCodes.LOGIN_USER_INVALID.getCode(), tokenResult.getMessage());
            }
            if (tokenRCode == UserLoginService.USER_BAN) {
                return ResultVO.failure(MsgCodes.LOGIN_USER_BAN);
            }
            if (tokenRCode == UserLoginService.RISK_FAILED) {
                return ResultVO.failure(MsgCodes.LOGIN_RISK_FAILED);
            }
            return ResultVO.failure();
        }
        UserTokenBean userTokenBean = tokenResult.target();
        Long userId = userTokenBean.getUserId();
        log.info("phoneLogin,userId={}", userId);
        if (userId == null) {
            return ResultVO.failure("用户不存在");
        }

        //查询被授权角色列表
        Result<List<RoleInfoAuthRefBean>> roleResult = roleService.getUserAuthRoles(appId, userId);
        if (RpcResult.isFail(roleResult)) {
            log.error("phoneLogin,getUserAuthRoles,error,rCode={}", roleResult.rCode());
            return ResultVO.failure("获取角色信息失败");
        }

        UserTokenVo userTokenVo = new UserTokenVo()
                .setAccessToken(userTokenBean.getAccessToken())
                .setRefreshToken(userTokenBean.getRefreshToken());

        List<RoleInfoAuthRefBean> roleList = roleResult.target();
        //没有授权角色：返回菜单, 设置登录角色 rCode=登录成功
        if (CollectionUtils.isEmpty(roleList)) {
            //设置角色为自己
            Result<Void> result = userLoginService.saveUserRole(SaveUserRoleReqBean.builder()
                    .userId(userId)
                    .appId(appId)
                    .deviceId(deviceId)
                    .build());
            if (RpcResult.isFail(result)) {
                return ResultVO.failure();
            }

            LoginUserInfoVo loginUserInfo = userLoginHandler.getLoginUserInfo(appId, userId, userTokenBean.getAccessToken());
            userTokenVo.setLoginType(loginUserInfo.getLoginType());
            userTokenVo.setUserInfo(loginUserInfo.getUserInfo());
            userTokenVo.setPermissionInfo(loginUserInfo.getPermissionInfo());
            log.info("phoneLogin,userId={},userTokenVo={}", userId, JsonUtil.dumps(userTokenVo));
            return ResultVO.success(userTokenVo);
        }

        //有授权角色：返回角色列表 rCode=请选择角色
        LoginUserInfoVo loginUserInfo = userLoginHandler.getLoginUserInfo(appId, userId, "");
        userTokenVo.setUserInfo(loginUserInfo.getUserInfo());
        userTokenVo.setRoles(RoleConvert.I.authRefInfoBeans2InfoVos(roleList));
        log.info("phoneLogin,userId={},userTokenVo={}", userId, JsonUtil.dumps(userTokenVo));
        return new ResultVO<>(MsgCodes.CHOSE_DATA_SCOPE.getCode(), null, userTokenVo);
    }

    /**
     * 刷新token
     * @return
     */
    @VerifyUserToken(required = false)
    @PostMapping("refreshToken")
    public ResultVO<UserTokenVo> refreshToken(@Validated @RequestBody RefreshTokenReqVo reqVo){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        //调用DC
        Result<UserTokenBean> userTokenResult = userLoginService.refreshToken(appId, reqVo.getRefreshToken());
        log.info("refreshToken,rCode={}", userTokenResult.rCode());
        //refreshToken过期，返回未登录
        if (userTokenResult.rCode() == UserLoginService.REFRESH_TOKEN_EXPIRED) {
            return ResultVO.failure(MsgCodes.NOT_LOGGED_IN);
        }
        if (RpcResult.isFail(userTokenResult)) {
            return ResultVO.failure();
        }
        log.info("refreshToken,userTokenBean={}", JsonUtil.dumps(userTokenResult.target()));
        return ResultVO.success(new UserTokenVo()
                .setRefreshToken(userTokenResult.target().getRefreshToken())
                .setAccessToken(userTokenResult.target().getAccessToken())
        );
    }


}
