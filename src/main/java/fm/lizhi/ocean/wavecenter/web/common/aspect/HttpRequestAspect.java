package fm.lizhi.ocean.wavecenter.web.common.aspect;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.context.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/19
 */
@Aspect
@Component
@Order(1)
@Slf4j
public class HttpRequestAspect {

    @Pointcut("@within(org.springframework.web.bind.annotation.RestController)")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        ServiceContext serviceContext = new ServiceContext(request, JsonUtil.dumps(joinPoint.getArgs()));
        Object res = null;
        try {
            ContextUtils.setContext(serviceContext);
            res = joinPoint.proceed();
            if (res instanceof ResultVO<?>) {
                ResultVO result = (ResultVO) res;
                serviceContext.setRCode(result.getRCode());
            }
        } catch (Exception e) {
            throw e;
        } finally {
            serviceContext.log();
            ContextUtils.clearContext();
        }
        return res;
    }


}
