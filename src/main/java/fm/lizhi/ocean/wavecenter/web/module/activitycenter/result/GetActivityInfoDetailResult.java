package fm.lizhi.ocean.wavecenter.web.module.activitycenter.result;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.activitycenter.vo.*;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GetActivityInfoDetailResult {

    /**
     * 活动ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 模板 ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long templateId;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动报名类型
     */
    private Integer applyType;

    /**
     * 活动审核状态
     */
    private Integer auditStatus;

    /**
     * 活动状态
     */
    private Integer activityStatus;

    /**
     * 活动联系方式
     */
    private String contact;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 活动联系方式号码
     */
    private String contactNumber;

    /**
     * 活动主持ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long hostId;

    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 活动分类名称
     */
    private String className;

    /**
     * 大分类名称
     */
    private String bigClassName;

    /**
     * 大类类型
     */
    private Integer bigClassType;

    /**
     * 活动目标，不超过100字
     */
    private String goal;

    /**
     * 活动介绍，不超过100字
     */
    private String introduction;

    /**
     * 模板封面
     */
    private String cover;

    /**
     * 活动海报图片
     */
    private String posterUrl;

    /**
     * 活动道具图片
     */
    private List<String> auxiliaryPropUrls;

    /**
     * 大类 ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bigClassId;


    /**
     * 分类 ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;


    /**
     * 活动工具
     */
    private List<ActivityToolVO> activityToolList;


    /**
     * 陪档主播
     */
    private List<UserVo> accompanyNjs;

    /**
     * 主持信息
     */
    private UserVo hostInfo;

    /**
     * 活动环节
     */
    private List<ActivityProcessVO> processes;


    /**
     * 房间背景信息
     */
    @Deprecated
    private DecorateVO roomBackgroundInfo;

    /**
     * 房间背景信息
     */
    private List<DecorateVO> roomBackgroundInfos;

    /**
     * 头像框信息
     */
    @Deprecated
    private DecorateVO avatarWidgetInfo;

    /**
     * 房间背景信息
     */
    private List<DecorateVO> avatarWidgetInfos;

    /**
     * 厅主信息
     */
    private UserVo njInfo;

    /**
     * 流量资源
     */
    private List<ActivityFlowResourceDetailVO> flowResourceDetails;

    /**
     * 房间公告
     */
    private String roomAnnouncement;

    /**
     * 房间公告图片
     */
    private List<String> roomAnnouncementImages;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 提报模式
     */
    private Integer model;


}
