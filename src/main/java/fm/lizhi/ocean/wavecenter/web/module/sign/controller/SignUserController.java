package fm.lizhi.ocean.wavecenter.web.module.sign.controller;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.OperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.*;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignUserService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.web.module.sign.param.UserDoSignParam;
import fm.lizhi.ocean.wavecenter.web.module.sign.processor.ISignUserProcessor;
import fm.lizhi.ocean.wavecenter.web.module.sign.result.UserApplyPlayerResult;
import fm.lizhi.ocean.wavecenter.web.module.sign.result.UserDoSignResult;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/10/5 15:52
 */
@Slf4j
@RestController
@RequestMapping("sign/user")
public class SignUserController {

    @Autowired
    private SignUserService signUserService;
    @Autowired
    private ProcessorFactory processorFactory;

    /**
     * 申请成为主播
     * @param param
     * @return
     */
    @PermissionCheck(passRole = RoleEnum.USER, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("applyPlayer")
    public ResultVO<UserApplyPlayerResult> applyPlayer(@Validated @RequestBody UserApplyPlayerParamVo param){
        RequestUserApplyPlayer rpcParam = RequestUserApplyPlayer.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .curUserId(ContextUtils.getContext().getUserId())
                .targetUserId(param.getTargetUserId())
                .build();
        Result<ResponseUserApplyPlayer> result = signUserService.applyPlayer(rpcParam);
        if (RpcResult.isFail(result)) {
            log.error("applyPlayer fail. rpcParam={}, rCode={}", JsonUtil.dumps(rpcParam), result.rCode());
            return ResultVO.failure(MsgCodes.FAIL);
        }

        Integer code = result.target().getCode();
        log.info("applyPlayerCode={},rpcParam={}", code, JsonUtil.dumps(rpcParam));
        if (code == SignUserService.APPLY_PLAYER_TARGET_NOT_ROOM || code == SignUserService.APPLY_PLAYER_TARGET_USER_NO_FAMILY){
            return ResultVO.failure("申请用户不是管理员");
        }
        if (code == SignUserService.APPLY_PLAYER_USER_VERIFY_NO_REF_USER) {
            return ResultVO.failure("实名身份证无关联账号");
        }
        ResultVO<Void> voidResultVO = SignCommonUtil.signCommonCodeCheck(result.target());
        if (!voidResultVO.isOK()) {
            return ResultVO.failure(voidResultVO.getRCode(), voidResultVO.getPrompt());
        }

        return ResultVO.success(new UserApplyPlayerResult().setContractId(result.target().getContractId()));
    }

    /**
     * 签约管理员的邀请
     * @param param
     * @return
     */
    @PermissionCheck(passRole = RoleEnum.USER, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("signAdminInvite")
    public ResultVO<Void> signAdminInvite(@Validated @RequestBody UserSignAdminInviteParamVo param){
        RequestUserSignAdminInvite rpcParam = RequestUserSignAdminInvite.builder()
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .curUserId(ContextUtils.getContext().getUserId())
                .playerSignId(param.getContractId())
                .operateTypeEnum(OperateTypeEnum.getByCode(param.getStatus()))
                .build();
        Result<ResponseUserSignAdminInvite> result = signUserService.signAdminInvite(rpcParam);
        if (RpcResult.isFail(result)) {
            log.error("signAdminInvite fail. rCode={},rpcParam={}", result.rCode(), JsonUtil.dumps(rpcParam));
            return ResultVO.failure(MsgCodes.FAIL);
        }
        return SignCommonUtil.signCommonCodeCheck(result.target());
    }

    /**
     * 申请成为厅主
     * 仅黑叶和西米需要发起
     */
    @PermissionCheck(passRole = RoleEnum.USER, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("/applyAdmin")
    public ResultVO<UserApplyAdminResVo> applyAdmin(@Validated @RequestBody ApplySignParamVo param){
        long userId = ContextUtils.getContext().getUserId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        ISignUserProcessor processor = getProcessor();
        //用户信息校验
        ResultVO<Void> checkRes = processor.applyAdminCheck(param);
        if (!checkRes.isOK()) {
            return ResultVO.failure(checkRes.getRCode(), checkRes.getPrompt());
        }

        RequestUserApplyAdmin rpcParam = RequestUserApplyAdmin.builder()
                .appId(appId)
                .curUserId(userId)
                .familyId(param.getFamilyId())
                .targetUserId(param.getTargetUserId())
                .build();

        Result<ResponseUserApplyAdmin> result = signUserService.applyAdmin(rpcParam);
        if (RpcResult.isFail(result)) {
            log.error("user applyAdmin fail. rCode={},rpcParam={}", result.rCode(), JsonUtil.dumps(rpcParam));
            return ResultVO.failure(MsgCodes.FAIL);
        }
        ResultVO<Void> voRes = SignCommonUtil.signCommonCodeCheck(result.target());
        if (!voRes.isOK()) {
            return ResultVO.failure(voRes.getRCode(), voRes.getPrompt());
        }

        return ResultVO.success(new UserApplyAdminResVo()
                .setSignId(result.target().getSignId())
                .setContractUrl(result.target().getContractUrl())
                .setContractId(result.target().getContractId())
        );
    }


    /**
     * 签署合同 成为厅管理员
     */
    @PermissionCheck(passRole = RoleEnum.USER, onlySelfLogin = true)
    @VerifyUserToken
    @PostMapping("/doSign")
    public ResultVO<UserDoSignResult> doSign(@Validated @RequestBody UserDoSignParam param){

        long curUserId = ContextUtils.getContext().getUserId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        RequestUserDoSign request = RequestUserDoSign.builder()
                .appId(appId)
                .curUserId(curUserId)
                .contractId(param.getContractId())
                .build();

        Result<ResponseUserDoSign> result = signUserService.doSign(request);
        if (RpcResult.isFail(result)) {
            log.error("user doSign fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
            return ResultVO.failure(MsgCodes.FAIL);
        }

        ResultVO<Void> voRes = SignCommonUtil.signCommonCodeCheck(result.target());
        if (!voRes.isOK()) {
            return ResultVO.failure(voRes.getRCode(), voRes.getPrompt());
        }

        return ResultVO.success(new UserDoSignResult()
                .setContractUrl(result.target().getContractUrl()));
    }

    /**
     * 检查用户状态
     * @param targetUserId 对方用户 ID，可不填，不填默认检查自己的
     */
    @VerifyUserToken
    @GetMapping("/userStatus")
    public ResultVO<SignUserStatusVo> userStatus(@RequestParam(value = "targetUserId", required = false) Long targetUserId){

        Long userId = ContextUtils.getContext().getUserId();
        if (targetUserId != null) {
            userId = targetUserId;
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        Result<ResponseUserInfoStatus> result = signUserService.infoStatus(RequestUserInfoStatus.builder()
                .appId(appId)
                .userId(userId)
                .build());
        if (RpcResult.isFail(result)) {
            log.error("infoStatus fail. appId={},userId={},rCode={}", appId, userId, result.rCode());
            return ResultVO.failure(MsgCodes.FAIL);
        }

        return ResultVO.success(new SignUserStatusVo()
                .setPlayerCenterStatus(result.target().getPlayerCenterStatus())
                .setInfoStatus(result.target().getInfoStatus())
        );
    }

    /**
     * 检查用户实名状态
     * @param targetUserId 对方用户 ID，可不填，不填默认检查自己的
     */
    @VerifyUserToken
    @GetMapping("/identifyStatus")
    public ResultVO<SignUserIdentifyStatusVo> identifyStatus(@RequestParam(value = "targetUserId", required = false) Long targetUserId){

        Long userId = ContextUtils.getContext().getUserId();
        if (targetUserId != null) {
            userId = targetUserId;
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        Result<ResponseUserIdentifyStatus> result = signUserService.identifyStatus(RequestUserIdentifyStatus.builder()
                .appId(appId)
                .userId(userId)
                .build());
        if (RpcResult.isFail(result)) {
            log.error("identifyStatus fail. appId={},userId={},rCode={}", appId, userId, result.rCode());
            return ResultVO.failure(MsgCodes.FAIL);
        }
        ResponseUserIdentifyStatus res = result.target();

        return ResultVO.success(new SignUserIdentifyStatusVo()
                .setBestSignStatus(res.getBestSignStatus())
                .setPlatformStatus(res.getPlatformStatus())
        );
    }

    private ISignUserProcessor getProcessor(){
        return processorFactory.getProcessor(ISignUserProcessor.class);
    }

}
