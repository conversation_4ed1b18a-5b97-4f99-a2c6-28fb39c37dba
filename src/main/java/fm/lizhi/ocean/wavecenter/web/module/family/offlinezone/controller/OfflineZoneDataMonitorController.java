package fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.anno.VerifyUserToken;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.convert.OfflineZoneDataMonitorConvert;
import fm.lizhi.ocean.wavecenter.web.module.family.offlinezone.vo.*;
import fm.lizhi.ocean.wavecenter.web.module.permission.handler.DataScopeHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 家族离线区域数据监控
 * <AUTHOR>
 * @date 2025/1/12
 */
@Slf4j
@RestController
@RequestMapping("family/offlinezone/data")
@PermissionCheck(passRole = {RoleEnum.FAMILY, RoleEnum.FAMILY_ADMIN})
public class OfflineZoneDataMonitorController {

    // TODO: 需要在 ServiceProvider 中添加 OfflineZoneDataMonitorService 的 Bean 配置
    // @Autowired
    // private OfflineZoneDataMonitorService offlineZoneDataMonitorService;
    
    @Autowired
    private DataScopeHandler dataScopeHandler;

    /**
     * 获取离线区域数据监控概览
     * @return
     */
    @VerifyUserToken
    @GetMapping("summary")
    public ResultVO<OfflineZoneDataSummaryVo> getDataSummary() {
        // 获取家族ID
        Long familyId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        
        log.info("getOfflineZoneDataSummary appId={}, familyId={}", appId, familyId);
        
        // TODO: 调用服务获取数据
        // Result<OfflineZoneDataSummaryBean> result = offlineZoneDataMonitorService.getDataSummary(appId, familyId);
        // if (RpcResult.isFail(result)) {
        //     log.error("getDataSummary,error,rCode={}", result.rCode());
        //     return ResultVO.failure();
        // }
        
        // TODO: 转换并返回数据
        // return ResultVO.success(OfflineZoneDataMonitorConvert.I.summaryBean2Vo(result.target()));
        
        // 临时返回空数据，等待服务实现
        return ResultVO.success(new OfflineZoneDataSummaryVo());
    }

    /**
     * 获取离线区域数据趋势图
     * @param metric 指标名称
     * @return
     */
    @VerifyUserToken
    @GetMapping("trend")
    public ResultVO<OfflineZoneDataTrendVo> getDataTrend(@RequestParam("metric") String metric) {
        // 获取家族ID
        Long familyId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        
        log.info("getOfflineZoneDataTrend appId={}, familyId={}, metric={}", appId, familyId, metric);
        
        // TODO: 调用服务获取趋势数据
        // Result<OfflineZoneDataTrendBean> result = offlineZoneDataMonitorService.getDataTrend(appId, familyId, metric);
        // if (RpcResult.isFail(result)) {
        //     log.error("getDataTrend,error,rCode={}", result.rCode());
        //     return ResultVO.failure();
        // }
        
        // TODO: 转换并返回数据
        // return ResultVO.success(OfflineZoneDataMonitorConvert.I.trendBean2Vo(result.target()));
        
        // 临时返回空数据，等待服务实现
        return ResultVO.success(new OfflineZoneDataTrendVo());
    }

    /**
     * 获取离线区域详细数据
     * @param paramVo 查询参数
     * @return
     */
    @VerifyUserToken
    @GetMapping("detail")
    public ResultVO<OfflineZoneDataDetailVo> getDataDetail(@Validated OfflineZoneDataParamVo paramVo) {
        // 获取家族ID
        Long familyId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        
        log.info("getOfflineZoneDataDetail appId={}, familyId={}, paramVo={}", appId, familyId, paramVo);
        
        // TODO: 调用服务获取详细数据
        // Result<OfflineZoneDataDetailBean> result = offlineZoneDataMonitorService.getDataDetail(
        //     OfflineZoneDataParamBean.builder()
        //         .appId(appId)
        //         .familyId(familyId)
        //         .startDate(paramVo.getStartDate())
        //         .endDate(paramVo.getEndDate())
        //         .pageNo(paramVo.getPageNo())
        //         .pageSize(paramVo.getPageSize())
        //         .build()
        // );
        // if (RpcResult.isFail(result)) {
        //     log.error("getDataDetail,error,rCode={}", result.rCode());
        //     return ResultVO.failure();
        // }
        
        // TODO: 转换并返回数据
        // return ResultVO.success(OfflineZoneDataMonitorConvert.I.detailBean2Vo(result.target()));
        
        // 临时返回空数据，等待服务实现
        return ResultVO.success(new OfflineZoneDataDetailVo());
    }

    /**
     * 获取离线区域监控配置
     * @return
     */
    @VerifyUserToken
    @GetMapping("config")
    public ResultVO<OfflineZoneMonitorConfigVo> getMonitorConfig() {
        // 获取家族ID
        Long familyId = ContextUtils.getContext().getSubjectId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        
        log.info("getOfflineZoneMonitorConfig appId={}, familyId={}", appId, familyId);
        
        // TODO: 调用服务获取监控配置
        // Result<OfflineZoneMonitorConfigBean> result = offlineZoneDataMonitorService.getMonitorConfig(appId, familyId);
        // if (RpcResult.isFail(result)) {
        //     log.error("getMonitorConfig,error,rCode={}", result.rCode());
        //     return ResultVO.failure();
        // }
        
        // TODO: 转换并返回数据
        // return ResultVO.success(OfflineZoneDataMonitorConvert.I.configBean2Vo(result.target()));
        
        // 临时返回空数据，等待服务实现
        return ResultVO.success(new OfflineZoneMonitorConfigVo());
    }
}
