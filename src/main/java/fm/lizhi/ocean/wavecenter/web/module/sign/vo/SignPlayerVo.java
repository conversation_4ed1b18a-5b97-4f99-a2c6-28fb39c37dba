
package fm.lizhi.ocean.wavecenter.web.module.sign.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.web.module.user.vo.UserVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/21 19:19
 */
@Data
public class SignPlayerVo{

    /**
     * 厅信息
     */
    private UserVo roomInfo;

    /**
     * 主播信息
     */
    private UserVo playerInfo;

    private Integer signStatus;

    /**
     * 结算比例 xx% 仅陪伴和西米有
     */
    private String settle;

    /**
     * 签约时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date signDate;

    /**
     * 到期时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date expireDate;

    /**
     * 解约时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date stopDate;

    /**
     * 合同ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long contractId;


}
