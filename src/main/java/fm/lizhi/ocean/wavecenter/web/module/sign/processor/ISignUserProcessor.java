package fm.lizhi.ocean.wavecenter.web.module.sign.processor;

import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.processor.BusinessEnvAwareProcessor;
import fm.lizhi.ocean.wavecenter.web.module.sign.vo.ApplySignParamVo;

/**
 * <AUTHOR>
 * @date 2024/10/23 14:31
 */
public interface ISignUserProcessor extends BusinessEnvAwareProcessor {

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return ISignUserProcessor.class;
    }

    /**
     * 用户申请为管理员检查
     * @param param
     * @return
     */
    ResultVO<Void> applyAdminCheck(ApplySignParamVo param);

}
